<?php
  defined( '_JEXEC' ) or die( 'Restricted access' );

  class ZenbaseCustomHelpers {
  	// Create a data atrribute for toggling JS events.  
  	//
    public static function toggleData($string) {
      // First strip any HTML tags
      $string = strip_tags($string);
      
      // Special case for Trip Extensions
      if (strtolower($string) === 'trip extensions' || strtolower($string) === 'extensions') {
        return 'trip-extensions';
      }
      
      // Special case for Trip Overview
      if (strtolower($string) === 'trip overview' || strtolower($string) === 'tripoverview') {
        return 'trip-overview';
      }
      
      // Convert to lowercase and replace non-word chars (except spaces) with nothing
      $string = strtolower($string);
      $string = preg_replace("/[^\w\s]+/", "", $string);
      
      // Replace spaces with hyphens
      $string = preg_replace("/\s+/", "-", trim($string));
      
      return $string;
    }

		public function standardCompare($a, $b) {
	    return strcmp($a, $b);
	  }

    public function organiseDatesByYear($dates) {
      // Initialize empty array
      $dates_array = array();
      
      // Check if dates is valid and not empty
      if (!is_array($dates) || empty($dates)) {
        return $dates_array;
      }
      
      $from_months = array();
      $adultTypeId = 1;
      
      foreach($dates as $date) {
        // Check if date objects and methods exist before using
        if (isset($date->start) && is_object($date->start) && method_exists($date->start, 'format')) {
          $dates_array[$date->start->format('Y')][] = $date;
        }
      }

      return $dates_array;
    }
    
    public function monthsBetweenDates($start, $end) {
      // Check if start and end are valid DateTime objects
      if (!is_object($start) || !is_object($end) ||
          !method_exists($start, 'format') || !method_exists($end, 'format')) {
        return 0;
      }

      $months = 0;
      $months = ($end->format('Y') - $start->format('Y')) * 12;
      $months -= $start->format('n');
      $months += $end->format('n');
      return $months <= 0 ? 0 : $months - 1;
    }

    /**
     * Calculate payment plan details for a holiday
     *
     * @param int $holidayId The holiday ID
     * @param array $dates Array of date objects with prices
     * @param int $depositPrice The deposit amount (default: 200)
     * @return array Array containing payment plan details
     */
    public function calculatePaymentPlan($holidayId, $dates = null, $depositPrice = 200) {
      $result = [
        'hasPaymentPlan' => false,
        'maxAvailableMonths' => 0,
        'monthlyPayment' => 0,
        'mostExpensivePrice' => null,
        'hasMinimumNumberTag' => false
      ];

      // Check if this holiday has special payment terms (minimum_number tag)
      $hasMinimumNumberTag = false;
      try {
        $tagsData = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $holidayId, 'tags');
        if ($tagsData && isset($tagsData['tags']) && isset($tagsData['tags']->items)) {
          foreach ($tagsData['tags']->items as $tagItem) {
            $tagContent = trim($tagItem->content);
            // Strip HTML tags (especially <p> tags) before parsing JSON
            $tagContent = strip_tags($tagContent);
            $tagContent = trim($tagContent);
            $decodedTag = json_decode($tagContent, true);
            if ($decodedTag && isset($decodedTag['minimum_number']) && $decodedTag['minimum_number'] === 'true') {
              $hasMinimumNumberTag = true;
              break;
            }
          }
        }
      } catch (Exception $e) {
        // Silently handle any errors with tag checking
      }

      $result['hasMinimumNumberTag'] = $hasMinimumNumberTag;

      // If no dates provided, return basic info
      if (!is_array($dates) || empty($dates)) {
        return $result;
      }

      $maxAvailableMonths = 0;
      $mostExpensivePrice = null;
      $todayDate = new DateTime();
      $todayDate->setTime(12, 0, 0, 0);

      // Process all dates to find maximum available months and most expensive price
      foreach ($dates as $dateItem) {
        // Handle both single date objects and arrays of dates
        $dateArray = is_array($dateItem) ? $dateItem : [$dateItem];

        foreach ($dateArray as $date) {
          // Skip if date doesn't have required properties
          if (!isset($date->start_date) || !isset($date->prices) || !is_array($date->prices) || !isset($date->prices[1])) {
            continue;
          }

          try {
            $serviceStart = new DateTime($date->start_date);
            $availableMonths = $this->monthsBetweenDates($todayDate, $serviceStart);

            // Calculate payment plan months based on holiday tags
            $paymentPlanMonths = 0;
            if ($hasMinimumNumberTag) {
              // For holidays with minimum_number tag, payment must be completed 6 months before departure
              if ($availableMonths >= 6) {
                $paymentPlanMonths = $availableMonths - 6;
                // Limit payment plan to 18 months maximum
                $paymentPlanMonths = min($paymentPlanMonths, 18);
              }
            } else {
              // Standard payment terms: booking at least 4 months before, completed 2 months before
              if ($availableMonths >= 4) {
                $paymentPlanMonths = $availableMonths - 2;
                // Limit payment plan to 18 months maximum
                $paymentPlanMonths = min($paymentPlanMonths, 18);
              }
            }

            if ($paymentPlanMonths > $maxAvailableMonths) {
              $maxAvailableMonths = $paymentPlanMonths;
            }

            // Find the most expensive price
            $datePrice = $date->prices[1];
            if (isset($datePrice->value) && ($mostExpensivePrice === null || $datePrice->value > $mostExpensivePrice->value)) {
              $mostExpensivePrice = $datePrice;
            }
          } catch (Exception $e) {
            // Skip this date if there's an error processing it
            continue;
          }
        }
      }

      $result['maxAvailableMonths'] = $maxAvailableMonths;
      $result['mostExpensivePrice'] = $mostExpensivePrice;

      // Calculate monthly payment if we have valid data
      if ($maxAvailableMonths > 0 && $mostExpensivePrice !== null && isset($mostExpensivePrice->value)) {
        $result['hasPaymentPlan'] = true;
        $minusDeposit = ($mostExpensivePrice->value) - $depositPrice;
        $result['monthlyPayment'] = ($maxAvailableMonths > 1) ? ($minusDeposit / $maxAvailableMonths) : $minusDeposit;
      }

      return $result;
    }

    /**
     * Calculate payment plan details for a specific date
     *
     * @param int $holidayId The holiday ID
     * @param object $date Single date object with prices
     * @param int $depositPrice The deposit amount (default: 200)
     * @return array Array containing payment plan details for this specific date
     */
    public function calculateDatePaymentPlan($holidayId, $date, $depositPrice = 200) {
      $result = [
        'hasPaymentPlan' => false,
        'paymentPlanMonths' => 0,
        'monthlyPayment' => 0,
        'hasMinimumNumberTag' => false
      ];

      // Check if this holiday has special payment terms (minimum_number tag)
      $hasMinimumNumberTag = false;
      try {
        $tagsData = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $holidayId, 'tags');
        if ($tagsData && isset($tagsData['tags']) && isset($tagsData['tags']->items)) {
          foreach ($tagsData['tags']->items as $tagItem) {
            $tagContent = trim($tagItem->content);
            // Strip HTML tags (especially <p> tags) before parsing JSON
            $tagContent = strip_tags($tagContent);
            $tagContent = trim($tagContent);
            $decodedTag = json_decode($tagContent, true);
            if ($decodedTag && isset($decodedTag['minimum_number']) && $decodedTag['minimum_number'] === 'true') {
              $hasMinimumNumberTag = true;
              break;
            }
          }
        }
      } catch (Exception $e) {
        // Silently handle any errors with tag checking
      }

      $result['hasMinimumNumberTag'] = $hasMinimumNumberTag;

      // Skip if date doesn't have required properties
      if (!isset($date->start_date) || !isset($date->prices) || !is_array($date->prices) || !isset($date->prices[1])) {
        return $result;
      }

      try {
        $todayDate = new DateTime();
        $todayDate->setTime(12, 0, 0, 0);
        $serviceStart = new DateTime($date->start_date);
        $availableMonths = $this->monthsBetweenDates($todayDate, $serviceStart);

        // Calculate payment plan months based on holiday tags
        $paymentPlanMonths = 0;
        if ($hasMinimumNumberTag) {
          // For holidays with minimum_number tag, payment must be completed 6 months before departure
          if ($availableMonths >= 6) {
            $paymentPlanMonths = $availableMonths - 6;
            // Limit payment plan to 18 months maximum
            $paymentPlanMonths = min($paymentPlanMonths, 18);
          }
        } else {
          // Standard payment terms: booking at least 4 months before, completed 2 months before
          if ($availableMonths >= 4) {
            $paymentPlanMonths = $availableMonths - 2;
            // Limit payment plan to 18 months maximum
            $paymentPlanMonths = min($paymentPlanMonths, 18);
          }
        }

        $result['paymentPlanMonths'] = $paymentPlanMonths;

        // Calculate monthly payment if payment plan is available
        if ($paymentPlanMonths > 0) {
          $result['hasPaymentPlan'] = true;
          $datePrice = $date->prices[1];
          if (isset($datePrice->value)) {
            $minusDeposit = ($datePrice->value) - $depositPrice;
            $result['monthlyPayment'] = ceil($minusDeposit / $paymentPlanMonths);
          }
        }
      } catch (Exception $e) {
        // Return default result if there's an error
      }

      return $result;
    }
  }
?>
