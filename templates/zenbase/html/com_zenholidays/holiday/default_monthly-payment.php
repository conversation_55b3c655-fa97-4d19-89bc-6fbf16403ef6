
<?php
// Check if this holiday has special payment terms (minimum_number tag)
$hasMinimumNumberTag = false;
$tagsData = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $this->item->id, 'tags');
if ($tagsData && isset($tagsData['tags']) && isset($tagsData['tags']->items)) {
    foreach ($tagsData['tags']->items as $tagItem) {
        $tagContent = trim($tagItem->content);
        // Strip HTML tags (especially <p> tags) before parsing JSON
        $tagContent = strip_tags($tagContent);
        $tagContent = trim($tagContent);
        $decodedTag = json_decode($tagContent, true);
        if ($decodedTag && isset($decodedTag['minimum_number']) && $decodedTag['minimum_number'] === 'true') {
            $hasMinimumNumberTag = true;
            break;
        }
    }
}

if ($paymentPanel) : ?>
		<?php foreach ($paymentPanel as $panel) : ?>
			<?php foreach ($panel->items as $item): ?>
				<?php $depositPrice = $item->xreference; ?>
			<?php endforeach; ?>
		<?php endforeach; ?>

		<?php foreach($dates as $key => $dateItem) : ?>

    <?php
    // Calculate payment plan using shared helper
    $customHelper = new ZenbaseCustomHelpers();
    $paymentPlan = $customHelper->calculatePaymentPlan($this->item->id, [$dateItem]);
    $maxAvailableMonths = $paymentPlan['maxAvailableMonths'];
    $mostExpensivePrice = $paymentPlan['mostExpensivePrice'];
    $depositPrice = 200;
    ?>

		// get the price from the last date in the array
		$lastDate = end($dateItem);
		$lastDatePrice = $lastDate->prices[1];

		?>
		<?php endforeach; ?>

		<!-- Montly Payment/Deposit Section  -->
		<h3 class="zen-title zen-title--font-semibold zen-capitalize">
			<?php echo JText::_('ZEN_SITE_MONTHLY_PAYMENT'); ?>
		</h3>
		<div class="row">
			<div class="zen-notice col-md-6 p-1">
				<div class="zen-notice__inner zen-notice--light">
					<div class="zen-notice__corner-text zen-uppercase">
						<?php echo JText::_('ZEN_SITE_FIRST'); ?>...
					</div>
					<div class="zen-notice__main">
						<div class="zen-notice__subheading">
							<p class="zen-text--lead mb-1">
								<?php echo JText::_('ZEN_SITE_DEPOSIT'); ?>
							</p>
						</div>
						<div class="zen-notice__price">
							<h2 class="zen-title zen-title--white zen-title--font-semibold">
								£<?php echo $depositPrice; ?>
							</h2>
						</div>
					</div>
					<div class="zen-notice__info">
					</div>
				</div>
			</div>

			<div class="zen-notice col-md-6 p-1">
				<div class="zen-notice__inner zen-notice--mid">
					<div class="zen-notice__corner-text zen-uppercase">
						<?php echo JText::_('ZEN_SITE_THEN'); ?>...
					</div>
					<div class="zen-notice__main">
						<div class="zen-notice__subheading">
							<p class="zen-text--lead mb-1">
							<?php echo $maxAvailableMonths . ' ' . JText::_('ZEN_HOLIDAY_MONTHLY_PAYMENTS_FROM'); ?>
							</p>
						</div>
						<div class="zen-notice__price">
						<h2 class="zen-title zen-title--white zen-title--font-semibold">
                        <?php
                        if ($paymentPlan['hasPaymentPlan'] && $maxAvailableMonths > 0) {
                            $monthlyAmount = ceil($paymentPlan['monthlyPayment']);
                            echo $mostExpensivePrice->currency_symbol . number_format($monthlyAmount);
                        } else {
                            // Fallback calculation
                            echo $fromPrice->currency_symbol . number_format(($fromPrice->value - $depositPrice) / 18);
                        }
                        ?>
                    </h2>
						</div>
					</div>
					<div class="zen-notice__info">
						<a data-bs-toggle="modal" data-bs-target="#exampleModal">
							<i class="zen-icon zen-icon--light fontello icon-info"></i>
						</a>
					</div>
				</div>
			</div>

			<!-- Modal -->
			<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
				<div class="modal-dialog">
						<div class="modal-body p-3">
						<h5 class="modal-title" id="exampleModalLabel">
								<?php echo JText::_('ZEN_SITE_FLEXIBLE_PAYMENTS'); ?>
							</h5>
							<hr>
							<button type="button" class="zen-modal__close-btn btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
							<ul class="ps-0 mt-3">
							<?php foreach ($paymentPanel as $paymentModal) : ?>
								<?php foreach ($paymentModal->items as $item): ?>
										<?php echo $item->content; ?>
									<?php endforeach; ?>
							<?php endforeach; ?>
							</ul>
						</div>
					</div>
				</div>

			<div class="zen-notice d-none col-md-4 p-1">
				<div class="zen-notice__inner zen-notice--dark">
					<div class="zen-notice__corner-text zen-uppercase">
						<?php echo JText::_('ZEN_SITE_OR'); ?>...
					</div>
					<div class="zen-notice__main">
						<div class="zen-notice__subheading">
							<p class="zen-text--lead mb-1">
								<?php echo JText::_('ZEN_SITE_1PAYMENT'); ?>
							</p>
						</div>
						<div class="zen-notice__price">
							<h2 class="zen-title zen-title--white zen-title--font-semibold">
								<?php echo $lastDatePrice->currency_symbol.number_format($lastDatePrice->value).JText::_('ZEN_HOLIDAY_PRICING_PP'); ?>
							</h2>
						</div>
					</div>
					<div class="zen-notice__info">
					</div>
				</div>
			</div>
		</div>
	<?php endif; ?>