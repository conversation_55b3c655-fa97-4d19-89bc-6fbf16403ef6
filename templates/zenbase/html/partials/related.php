<?php
// No direct access.
//
defined('_JEXEC') or die;
include_once(JPATH_BASE . '/templates/zenbase/template.php');
include_once(JPATH_BASE . '/templates/zenbase/custom_helpers.php');
jimport('mrzen.classes.utils');
jimport('joomla.language.text');
jimport('joomla.factory');

$item_locale = [];
// Standard category management.
//
if (isset($item->categories) && is_array($item->categories)) {
	foreach ($item->categories as $key => $category) {
		if (isset($category->parent) && isset($category->parent->alias) && $category->parent->alias == 'altitude-range') {
			$item_altRange[$category->parent->alias] = $category->title;
		}
		if (isset($category->parent) && isset($category->parent->alias) && $category->parent->alias == 'activity-level') {
			$item_actLevel[$category->parent->alias] = $category->title;
		}
	}
}
// Locations.
//
if (isset($item->locations) && is_array($item->locations)) {
	foreach ($item->locations as $location) {
		if (isset($location->name)) {
			array_push($item_locale, $location->name);
		}
	}
}
// Variables.
//
$app = JFactory::getApplication();
$doc = JFactory::getDocument();
$template = $app->getTemplate(true);
$customHelper = new ZenbaseCustomHelpers($template);

// Get months directly
$item_months = [];
if (isset($item->dates) && is_array($item->dates)) {
	foreach($item->dates as $date) {
		if (isset($date->start) && is_object($date->start) && method_exists($date->start, 'format')) {
			$startMonth = $date->start->format('M');
			if (!in_array($startMonth, $item_months)) {
				$item_months[] = $startMonth;
			}
		}

		if (isset($date->end) && is_object($date->end) && method_exists($date->end, 'format')) {
			$endMonth = $date->end->format('M');
			if (!in_array($endMonth, $item_months)) {
				$item_months[] = $endMonth;
			}
		}
	}
	sort($item_months);
}

// Organize dates by year
$dates = [];
if (isset($item->dates) && is_array($item->dates)) {
	foreach($item->dates as $date) {
		if (isset($date->start) && is_object($date->start) && method_exists($date->start, 'format')) {
			$dates[$date->start->format('Y')][] = $date;
		}
	}
}

$item_class = (isset($item_sppbData)) ? "sppb-card-item" : "";
$item_duration = isset($item->default_version) && isset($item->default_version->number_of_days) ? $item->default_version->number_of_days : null;
$item_fromPrice = isset($item->from_price) && isset($item->from_price->price) ? $item->from_price->price : null;

// Check if $item_fullHeightCards is set before using it
if (isset($item_fullHeightCards) && $item_fullHeightCards) {
	$item_cardClass = "zen-card--full-height";
} else {
	$item_cardClass = "";
}

// Safely handle image initialization
if (isset($item->images) && is_array($item->images) && !empty($item->images) && isset($item->images[0])) {
	$item_image = JHtmlImages::getCachedImageUrl($item->images[0], 515, 275, '', array(), isset($item->name) ? $item->name : '');
} else {
	$item_image = "https://via.placeholder.com/515x275&text=...";
}

// Only call organiseDatesByYear if $item->dates is set and valid
if (isset($item->dates) && is_array($item->dates)) {
	$dates = $customHelper->organiseDatesByYear($item->dates);
}

// Simple payment calculation to match default_hero approach
$depositPrice = 200; // Set deposit price
$maxAvailableMonths = 18; // Fixed 18 months like hero section
$mostExpensivePrice = null;

// Find the most expensive price from all dates
if (is_array($dates)) {
	foreach ($dates as $key => $dateItem) {
		if (is_array($dateItem)) {
			foreach ($dateItem as $date) {
				// Find the most expensive price - safely access with checks
				if (isset($date->prices) && is_array($date->prices) && isset($date->prices[1]) && isset($date->prices[1]->value)) {
					if ($mostExpensivePrice === null || $date->prices[1]->value > $mostExpensivePrice->value) {
						$mostExpensivePrice = $date->prices[1];
					}
				}
			}
		}
	}
}

// Calculate the monthly payment using fixed 18 months
$monthlyPayment = 0;
if ($mostExpensivePrice !== null && isset($mostExpensivePrice->value)) {
	$minusDeposit = ($mostExpensivePrice->value) - $depositPrice;
	$monthlyPayment = $minusDeposit / $maxAvailableMonths;
}

?>



<div class="<?php echo $item_class; ?>">

	<div class="zen-card zen-card--related <?= $item_cardClass; ?>">
		<a href="/holidays/<?php echo $item->alias; ?>" class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block">
			<div class="zen-card__body">
				<div class="zen-card__image zen-card__image--max-height mb-3">
					<img class="img-fluid" src="<?php echo $item_image; ?>" alt="<?php echo $item->name; ?>">
					<div class="zen-card__image-overlay">
						<ul class="zen-list zen-list--inline">
							<li class="zen-text zen-text--light overlay-message text-center">
								<?php echo JText::_('ZEN_SEARCH_RESULTS_AVAILABLE_DATES'); ?>
							</li>
							<?php $i = 0; ?>
							<?php $limitMonths = $item_months; ?>
							<?php foreach ($limitMonths as $mKey => $departMonth) : ?>
								<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										<?php echo $departMonth; ?>
									</span>
								</li>
								<?php $i++; ?>
							<?php endforeach; ?>
						</ul>
					</div>
				</div>

				<div class="zen-card__info-wrap">
					<?php $cardHeading = ($item->name) ? $item->name : $item->title; ?>
					<h5 class="zen-title zen-title--font-semibold zen-capitalize">
						<?php echo $cardHeading; ?>
					</h5>
					<?php if ($item_locale || $item_duration): ?>
						<p class="zen-text zen-text--subtitle mb-1">
							<?php
							$locationText = '';
							if (is_array($item_locale) && !empty($item_locale)) {
								$locationText = implode(", ", $item_locale);
							} elseif (!empty($item_locale)) {
								$locationText = $item_locale;
							}
							if (!empty($locationText)) {
								echo "<span>" . $locationText . "</span>";
							}
							if ($item_duration) {
								$thisDuration = ($item_duration == 1)
									? JText::_('ZEN_HOLIDAY_SINGLE_NIGHT')
									: JText::_('ZEN_HOLIDAY_NIGHTS');
								echo "<span>" . $item_duration . " " . $thisDuration . "</span>";
							}
							?>
						</p>
					<?php endif; ?>
					<div class="zen-card__info">
						<div class="d-flex justify-content-start align-items-center">
							<img src="/templates/zenbase/icons/altitude.svg" alt="">
							<span><?php echo $item_altRange['altitude-range']; ?></span>
						</div>
						<div class="d-flex justify-content-end align-items-center">
									<div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
										<button class="difficulty-info-btn d-flex align-items-center"
											data-id="<?php echo $item->id; ?>"
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#global-difficultyModal-<?php echo strtolower(str_replace(' ', '-', $item_actLevel['activity-level'])); ?>">
											<span class="difficulty-icon">
												<?php echo file_get_contents(JPATH_BASE . '/templates/zenbase/icons/info-rounded.svg'); ?>
											</span>
											<span class="difficulty-text"><?php echo $item_actLevel['activity-level']; ?></span>
											<?php
											// Determine circle color based on difficulty level
											$difficultyColor = '#95C623'; // Default to Easy
											$difficultyLevel = strtolower($item_actLevel['activity-level']);

											if ($difficultyLevel === 'hardcore') {
												$difficultyColor = '#CD5334';
											} elseif ($difficultyLevel === 'challenging') {
												$difficultyColor = '#FE7720';
											} elseif ($difficultyLevel === 'moderate') {
												$difficultyColor = '#FFBF06';
											}
											?>
											<span class="difficulty-indicator ms-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: <?php echo $difficultyColor; ?>"></span>
										</button>
							</div>
						</div>
					</div>
					<div class="zen-text zen-text--text-df mb-3">
						<?php
						try {
							$previewBullets = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $item->id, 'preview-bullets');
							if ($previewBullets && isset($previewBullets['preview-bullets']) && isset($previewBullets['preview-bullets']->items)) {
								foreach ($previewBullets['preview-bullets']->items as $bullet) {
									echo $bullet->content;
								}
							}
						} catch (Exception $e) {
							// Silently handle any errors with preview bullets
						}
						?>
					</div>
					<div class="zen-flex-end zen-price">
						<div class="row w-100 m-0">
							<div class="col-12 d-flex justify-content-between align-items-center p-0">
								<span class="">
									<?php echo JText::_('ZEN_CARD_FROM') . " "; ?>
									<span><?php echo $item_fromPrice->currency_symbol . number_format($item_fromPrice->value) . JText::_('ZEN_HOLIDAY_PRICING_PP'); ?></span>
								</span>
								<span class="zen-text zen-text--default-light zen-uppercase mx-2">
									<?php echo JText::_('ZEN_SITE_OR'); ?>
								</span>
								<span class="">
									<!-- Monthly Payment Pill  -->
									<?php if ($maxAvailableMonths > 1) : ?>
										<span class="p-0">
											<?php echo $mostExpensivePrice->currency_symbol . number_format(ceil($monthlyPayment), 0); ?> / <?php echo $maxAvailableMonths; ?>
											<?php echo JText::_('ZEN_HOLIDAY_MONTHS') ?>
										</span>
									<?php elseif ($maxAvailableMonths == 1) : ?>
										<span class="p-0">
											<?php echo $mostExpensivePrice->currency_symbol . number_format(ceil($minusDeposit), 0); ?> / 1
											<?php echo JText::_('ZEN_HOLIDAY_MONTH') ?>
										</span>
									<?php endif; ?>
							</div>
						</div>
						</span>
					</div>
				</div>


			</div>
		</a>
	</div>
</div>

<!-- Global difficulty modals are now included at the root level -->

<?php $item = false; ?>
